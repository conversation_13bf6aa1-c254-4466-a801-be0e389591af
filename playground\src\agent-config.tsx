import { Button } from "@@/antd/es";
import { SearchOutlined, SortAscendingOutlined } from "@ant-design/icons";
import { dynamicPageConfigFactory } from "@cscs-agent/agents";
import type { AgentChatConfig } from "@cscs-agent/core";
import { PromptTemplate } from "@cscs-agent/presets";

import InsertTag from "./widgets/insert-tag";
import InsertText from "./widgets/insert-text";

const PreviewWidget = () => {
  return <Button>PreviewWidget</Button>;
};

const dynamicPageAgentConfig = dynamicPageConfigFactory({
  saveApiUrl: () => {
    const host = location.host;
    const protocol = location.protocol;
    return `${protocol}//${host}/cluster-api/page/system/dynamic/page/manage/savePageForAi`;
  },
  previewUrl: (id: string) => `http://************:8002/dynamic-page/temporary-preview?id=${id}`,
});

export const config: AgentChatConfig = {
  agents: [
    dynamicPageAgentConfig,
    {
      name: "问答助手",
      code: "dynamic-page-qa",
      logo: "/assets/dynamic-page-qa-logo.png",
      welcome: "Hi，欢迎使用问答助手",
      description: "面向动态页面管理场景，提供自然语言交互式解答，提升配置效率",
      message: {
        blocks: {
          widgets: [],
        },
        slots: {
          footer: {
            widgets: [],
          },
        },
      },
      prompts: [],
      commands: [
        {
          name: "",
          action: (params) => {},
        },
      ],
      suggestions: [],
      sender: {
        slots: {
          footer: {
            widgets: [],
          },
        },
      },
      sidePanel: {
        render: {
          widgets: [
            {
              code: "PreviewWidget",
              component: PreviewWidget,
            },
          ],
        },
      },
      request: {
        chat: {
          url: "/chat/completion",
          headers: {},
          method: "get",
        },
      },
    },
    {
      name: "测试智能体",
      code: "default",
      message: {
        blocks: {
          widgets: [
            {
              code: "@DynamicPage/PreviewButton",
              component: PreviewWidget,
            },
          ],
        },
        slots: {
          footer: {
            widgets: [],
          },
        },
      },
      prompts: [
        {
          icon: <SearchOutlined />,
          title: "字段查询",
          description: "根据字段查询列表",
          prompt: "请查询{{[企业名称]}}获取企业信息",
        },
        {
          icon: <SortAscendingOutlined />,
          title: "排序",
          description: "配置列表排序",
          prompt: "请对列表进行排序",
        },
      ],
      commands: [
        {
          name: "",
          action: () => {},
        },
      ],
      suggestions: [],
      sender: {
        slots: {
          headerPanel: {
            widgets: [
              {
                code: "PromptTemplate",
                component: PromptTemplate,
              },
            ],
          },
          header: {
            widgets: [
              {
                code: "InsertText",
                component: InsertText,
              },
              {
                code: "InsertTag",
                component: InsertTag,
              },
            ],
          },
          footer: {
            widgets: [],
          },
        },
      },
      sidePanel: {
        render: {
          widgets: [
            {
              code: "PreviewWidget",
              component: PreviewWidget,
            },
          ],
        },
      },
      request: {
        chat: {
          url: "/chat/test",
          headers: {},
          method: "get",
        },
      },
    },
  ],
};
