import { Input, InputRef, Modal, Spin, Tooltip } from "antd";
import React, { useCallback, useEffect, useMemo } from "react";
import InfiniteScroll from "react-infinite-scroll-component";
import { useNavigate } from "react-router";

import { useSubscribeCommand } from "@/command";
import { useActiveAgentCode, useActiveConversationId, useConversations } from "@/core";
import { del, get, put } from "@/request";
import { BuildInCommand, ConversationHistoryResponse } from "@/types";
import { DeleteOutlined, EditOutlined, RedoOutlined } from "@ant-design/icons";
import { Conversations } from "@ant-design/x";
import { Icon } from "@cscs-agent/icons";

const confirm = Modal.confirm;

interface ConversationTitleProps {
  title: string;
  onFinished: (title: string) => void;
  editable: boolean;
  onCancel: () => void;
}

interface ConversationsProps {
  height: string;
}

const ConversationTitle: React.FC<ConversationTitleProps> = (props) => {
  const { title, onFinished, onCancel, editable } = props;
  const [value, setValue] = React.useState(title);
  const inputRef = React.useRef<InputRef>(null);

  const confirmEdit = () => {
    onFinished(value);
  };

  const cancel = () => {
    setTimeout(() => {
      onCancel();
    }, 100);
  };

  useEffect(() => {
    if (editable) {
      inputRef.current?.focus();
    }
  }, [editable]);

  if (!editable) {
    return (
      <Tooltip title={title}>
        <span>{title}</span>
      </Tooltip>
    );
  }

  return (
    <Input
      ref={inputRef}
      value={value}
      onChange={(e) => {
        setValue(e.target.value);
      }}
      onClick={(e) => {
        e.stopPropagation();
      }}
      onBlur={cancel}
      suffix={
        <Tooltip title="确认修改">
          <EditOutlined
            onClick={(e) => {
              confirmEdit();
              e.stopPropagation();
            }}
          />
        </Tooltip>
      }
    />
  );
};

const ConversationContainer: React.FC<ConversationsProps> = (props) => {
  const { height } = props;
  const [conversations, setConversations] = useConversations();
  const [activeConversationId] = useActiveConversationId();
  const [, setActiveAgentCode] = useActiveAgentCode();
  const [page, setPage] = React.useState(1);
  const [hasMore, setHasMore] = React.useState(true);
  const [currentRenameConversationId, setCurrentRenameConversationId] = React.useState<string | null>(null);

  useSubscribeCommand(BuildInCommand.NewConversationCreated, () => {
    // 重新获取最新的会话列表
    setPage(1);
    setHasMore(true);
    setConversations([]);
    loadMoreData(1);
  });

  const navigate = useNavigate();

  const remove = (id: string) => {
    del(`/conversation/${id}`).then(() => {
      setConversations((conversations) => {
        // 如果删除的是当前激活的会话，跳转到首页
        if (activeConversationId === id) {
          navigate("/");
        }
        return conversations.filter((i) => i.id !== id);
      });
    });
  };

  const rename = useCallback(async (id: string, title: string) => {
    setCurrentRenameConversationId(null);

    put(`/conversation/${id}`, { title }).then(() => {
      setConversations((conversations) => {
        const conversation = conversations.find((i) => i.id === id);
        if (conversation) {
          conversation.title = title;
        }
        return [...conversations];
      });
    });
  }, []);

  const items = useMemo(() => {
    return conversations.map((i) => ({
      key: i.id,
      conversation_id: i.id,
      label: (
        <ConversationTitle
          title={i.title}
          editable={currentRenameConversationId === i.id}
          onFinished={(title) => {
            rename(i.id, title);
          }}
          onCancel={() => {
            setCurrentRenameConversationId(null);
          }}
        />
      ),
      group: i.group,
    }));
  }, [conversations, currentRenameConversationId, rename]);

  const getConversationDetail = (id: string) => {
    // TODO any类型
    get(`/conversation/${id}`).then((res: any) => {
      const data = res.data.data;
      setActiveAgentCode(data.current_agent_code);
    });
  };

  useEffect(() => {
    if (activeConversationId) {
      getConversationDetail(activeConversationId);
    }
  }, [activeConversationId]);

  const loadMoreData = (currentPage: number) => {
    get<ConversationHistoryResponse>("/conversation", {
      page: currentPage,
      size: 20,
    }).then((res) => {
      const data = res.data.data;

      const list = data.map((i) => {
        let group = "";
        const now = new Date();
        const date = new Date(i.updated_at ?? 0);

        // Reset time parts to compare dates only
        const nowDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const itemDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

        // Calculate difference in days
        const diffTime = nowDate.getTime() - itemDate.getTime();
        const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

        // Today
        if (diffDays === 0) {
          group = "今天";
        }
        // Yesterday
        else if (diffDays === 1) {
          group = "昨天";
        }
        // in 7 days
        else if (diffDays > 1 && diffDays <= 7) {
          group = "7天内";
        }
        // in 30 days
        else if (diffDays > 7 && diffDays <= 30) {
          group = "30天内";
        } else {
          group = "更早的";
        }

        return {
          id: i.id,
          title: i.title,
          // TODO 修改messages 转换类型
          currentAgentCode: i.current_agent_code,
          group,
        };
      });
      if (currentPage >= res.data.pagination.page_total) {
        setHasMore(false);
      }
      setPage(currentPage + 1);
      setConversations((conversations) => [...conversations, ...list]);
    });
  };

  useEffect(() => {
    loadMoreData(page);
  }, []);

  const changeToRename = (id: string) => {
    setCurrentRenameConversationId(id);
  };

  // TODO 优化高度定义

  return (
    <div>
      <div className="ag:pr-4 ag:pl-6 ag:py-2 ag:flex ag:text-dark ag:justify-between">
        <div>
          <Icon icon="Chat" />
          <span className="ag:pl-2 ag:text-sm">最近对话</span>
        </div>
        {/* <Button type="text" icon={<SearchOutline className="ag:w-[18px]" />} /> */}
      </div>
      <div
        id="cscs—agent-conversation-scrollable"
        className="ag:overflow-y-auto"
        style={{
          height,
        }}
      >
        <InfiniteScroll
          dataLength={conversations.length}
          next={() => loadMoreData(page)}
          hasMore={hasMore}
          loader={
            <div className="ag:text-center">
              <Spin indicator={<RedoOutlined spin />} size="small" />
            </div>
          }
          scrollableTarget="cscs—agent-conversation-scrollable"
          className="ag:overflow-hidden"
        >
          <Conversations
            items={items}
            activeKey={activeConversationId ?? ""}
            onActiveChange={async (val) => {
              navigate(`/chat/${val}`);
              setCurrentRenameConversationId(null);
            }}
            groupable={{
              sort(a, b) {
                const groupOrder = ["今天", "昨天", "7天内", "30天内", "更早的"];
                const aIndex = groupOrder.indexOf(a);
                const bIndex = groupOrder.indexOf(b);
                return aIndex - bIndex;
              },
            }}
            styles={{ item: { padding: "0 12px" } }}
            menu={(item) => ({
              items: [
                {
                  label: "重命名",
                  key: "rename",
                  icon: <EditOutlined />,
                  onClick: (e) => {
                    changeToRename(item.conversation_id);
                    e.domEvent.stopPropagation();
                  },
                },
                {
                  label: "删除",
                  key: "delete",
                  icon: <DeleteOutlined />,
                  danger: true,
                  onClick: (e) => {
                    // 用户二次确认删除
                    confirm({
                      title: "删除会话",
                      content: "确定要删除会话吗？",
                      onOk() {
                        remove(item.conversation_id);
                      },
                    });
                    e.domEvent.stopPropagation();
                  },
                },
              ],
            })}
          />
        </InfiniteScroll>
      </div>
    </div>
  );
};

export default ConversationContainer;
