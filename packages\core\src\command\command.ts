import Emitter from "eventemitter3";
import { useContext, useEffect } from "react";

import { AgentChatContext } from "@/core/state/context";
import { CommandCallback, CommandEmitter, CommandUnsubscribe, IBaseCommandParams } from "@/types";

/**
 * 命令事件发射器
 * 用于发布和订阅命令事件
 */
export const commandEmitter: CommandEmitter = new Emitter();

function createCommandName(name: string, scope: string): string {
  return `@command/${scope}:${name}`;
}

/**
 * 订阅命令
 * 在React组件中订阅命令事件，组件卸载时自动取消订阅
 *
 * @param name 命令名称，只允许使用字母、数字和下划线
 * @param callback 命令回调函数
 * @returns 取消订阅函数
 */
export const useSubscribeCommand = (name: string, callback: CommandCallback): CommandUnsubscribe => {
  const { agentId } = useContext(AgentChatContext);

  const unsubscribe = () => {
    commandEmitter.off(createCommandName(name, agentId), callback);
  };

  useEffect(() => {
    commandEmitter.on(createCommandName(name, agentId), callback);
    return () => {
      commandEmitter.off(createCommandName(name, agentId), callback);
    };
  }, [name, agentId, callback]);

  return unsubscribe;
};

/**
 * 运行命令
 * 触发指定名称的命令事件
 *
 */
export const useCommandRunner = () => {
  const { agentId } = useContext(AgentChatContext);

  const runner = (name: string, params?: IBaseCommandParams) => {
    // 延迟执行，确保所有订阅已经更新，命令在下一次渲染之前执行
    setTimeout(() => {
      commandEmitter.emit(createCommandName(name, agentId), params);
    }, 0);
  };

  return runner;
};
