import { resolve } from "path";

import { defineConfig } from "vite";
import svgr from "vite-plugin-svgr";

import typescript from "@rollup/plugin-typescript";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  plugins: [
    typescript({
      tsconfig: resolve(__dirname, mode === "development" ? "tsconfig.json" : "tsconfig.build.json"),
    }),
    svgr({
      include: "**/*.svg?react",
    }),
  ],
  build: {
    lib: {
      entry: resolve(__dirname, "src/index.ts"),
      name: "@cscs-agent/presets",
      fileName: (format) => `index.${format}.js`,
      formats: ["es", "cjs"],
    },
    sourcemap: mode === "development",
    outDir: "dist",
    rollupOptions: {
      // Make sure to externalize deps that shouldn't be bundled
      external: [
        "nanoid",
        // Peer dependencies
        "react",
        "react-dom",
        "react/jsx-runtime",
      ],
      output: {
        // Provide global variables to use in the UMD build
        globals: {
          react: "React",
          "react-dom": "ReactDOM",
          "react/jsx-runtime": "ReactJSXRuntime",
        },
        assetFileNames: "icons.css",
      },
    },
  },
  resolve: {
    alias: {
      "@": resolve(__dirname, "src"),
    },
  },
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
      },
    },
  },
}));
