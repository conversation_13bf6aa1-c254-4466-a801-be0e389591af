import { Tooltip } from "antd";
import React from "react";

import { BuildInCommand, useCommandRunner } from "@cscs-agent/core";
import { Icon } from "@cscs-agent/icons";

interface PreviewButtonProps {
  id: string;
  name: string;
}

const PreviewButton: React.FC<PreviewButtonProps> = (props) => {
  const { id, name } = props ?? {};
  const runner = useCommandRunner();

  const handlePreview = () => {
    runner(BuildInCommand.RenderSidePanel, {
      widgetCode: "@DynamicPage/SidePanelPreview",
      widgetProps: {
        id,
        name,
      },
    });

    runner(BuildInCommand.OpenSidePanel, {
      widgetCode: "@DynamicPage/SidePanelPreview",
      width: 500,
    });
  };

  return (
    <Tooltip title="点击预览">
      <div
        className="agents:mt-1 agents:mb-1 agents:px-3 agents:py-4 agents:flex agents:border agents:rounded-md agents:border-gray-300 agents:w-[300px] agents:cursor-pointer agents:bg-white"
        style={{
          boxShadow: "0px 3px 4px -3px rgba(0,0,0,0.08)",
        }}
        onClick={handlePreview}
      >
        <Icon
          icon="WebOutline"
          style={{
            color: "#6C90F2",
          }}
        />
        <span className="agents:ml-2">{name}</span>
      </div>
    </Tooltip>
  );
};

export default PreviewButton;
