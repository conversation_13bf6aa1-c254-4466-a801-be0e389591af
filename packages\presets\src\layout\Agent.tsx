import React from "react";

import { AgentWelcome, PopularQuestion } from "@/components";

interface DefaultAgentLayoutProps {
  welcome?: React.ReactNode;
  sender: React.ReactNode;
  popularQuestion?: React.ReactNode;
}

const DefaultAgentLayout: React.FC<DefaultAgentLayoutProps> = (props) => {
  const { sender, welcome, popularQuestion } = props;

  return (
    <div className="presets:flex presets:h-full presets:w-full presets:bg-[#FBFCFD]">
      {/* Middle content - adaptive width */}
      <div className="presets:flex-1 presets:h-full presets:flex presets:flex-col">
        <div className="presets:flex-1 presets:overflow-y-auto presets:flex presets:flex-col presets:items-center presets:justify-center">
          {welcome ? welcome : <AgentWelcome />}
          {popularQuestion ? popularQuestion : <PopularQuestion />}
        </div>
        <div className="presets:w-[800px] presets:mx-auto presets:pb-4 presets:mb-4">{sender}</div>
      </div>
    </div>
  );
};

export default DefaultAgentLayout;
