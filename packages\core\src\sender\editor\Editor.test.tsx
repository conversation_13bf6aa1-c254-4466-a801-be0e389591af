import { beforeEach, describe, expect, it, vi } from "vitest";

import { render, screen } from "@testing-library/react";

import Editor from "./Editor";

// Mock TipTap modules
vi.mock("@tiptap/react", () => ({
  EditorContent: ({ editor }: any) => (
    <div data-testid="editor-content" data-editor={editor ? "initialized" : "null"}>
      Editor Content
    </div>
  ),
  useEditor: vi.fn(),
}));

vi.mock("@tiptap/starter-kit", () => ({
  default: {},
}));

vi.mock("./EditableTag", () => ({
  default: {},
}));

vi.mock("./Select", () => ({
  default: {},
}));

vi.mock("./Tag", () => ({
  default: {},
}));

describe("Editor Component", () => {
  const mockEditor = {
    commands: {
      focus: vi.fn(),
      clearContent: vi.fn(),
      insertContent: vi.fn(),
    },
    getText: vi.fn(() => "test content"),
    getHTML: vi.fn(() => "<p>test content</p>"),
  };

  beforeEach(async () => {
    vi.clearAllMocks();
    const { useEditor } = await import("@tiptap/react");
    vi.mocked(useEditor).mockReturnValue(mockEditor as any);
  });

  it("renders without crashing", () => {
    const mockOnChange = vi.fn();

    render(<Editor onChange={mockOnChange} />);

    expect(screen.getByTestId("editor-content")).toBeTruthy();
  });

  it("configures useEditor with handleDOMEvents when onEnterPress is provided", async () => {
    const mockOnChange = vi.fn();
    const mockOnEnterPress = vi.fn();

    render(<Editor onChange={mockOnChange} onEnterPress={mockOnEnterPress} />);

    const { useEditor } = await import("@tiptap/react");
    expect(vi.mocked(useEditor)).toHaveBeenCalledWith(
      expect.objectContaining({
        editorProps: expect.objectContaining({
          handleDOMEvents: expect.objectContaining({
            keydown: expect.any(Function),
          }),
        }),
      }),
    );
  });

  it("calls onEnterPress when Enter key is pressed", async () => {
    const mockOnChange = vi.fn();
    const mockOnEnterPress = vi.fn();

    render(<Editor onChange={mockOnChange} onEnterPress={mockOnEnterPress} />);

    // Get the configuration passed to useEditor
    const { useEditor } = await import("@tiptap/react");
    const useEditorConfig = vi.mocked(useEditor).mock.calls[0][0];
    const keydownHandler = useEditorConfig?.editorProps?.handleDOMEvents?.keydown;

    // Simulate Enter key press
    const mockEvent = {
      key: "Enter",
      preventDefault: vi.fn(),
      shiftKey: false,
    };

    keydownHandler?.(null as any, mockEvent as any);

    expect(mockOnEnterPress).toHaveBeenCalledWith(mockEvent);
  });

  it("does not call onEnterPress for non-Enter keys", async () => {
    const mockOnChange = vi.fn();
    const mockOnEnterPress = vi.fn();

    render(<Editor onChange={mockOnChange} onEnterPress={mockOnEnterPress} />);

    // Get the configuration passed to useEditor
    const { useEditor } = await import("@tiptap/react");
    const useEditorConfig = vi.mocked(useEditor).mock.calls[0][0];
    const keydownHandler = useEditorConfig?.editorProps?.handleDOMEvents?.keydown;

    // Simulate non-Enter key press
    const mockEvent = {
      key: "a",
      preventDefault: vi.fn(),
      shiftKey: false,
    };

    keydownHandler?.(null as any, mockEvent as any);

    expect(mockOnEnterPress).not.toHaveBeenCalled();
  });

  it("works correctly when onEnterPress is not provided", async () => {
    const mockOnChange = vi.fn();

    render(<Editor onChange={mockOnChange} />);

    // Get the configuration passed to useEditor
    const { useEditor } = await import("@tiptap/react");
    const useEditorConfig = vi.mocked(useEditor).mock.calls[0][0];
    const keydownHandler = useEditorConfig?.editorProps?.handleDOMEvents?.keydown;

    // Simulate Enter key press - should not throw error
    const mockEvent = {
      key: "Enter",
      preventDefault: vi.fn(),
      shiftKey: false,
    };

    expect(() => keydownHandler?.(null as any, mockEvent as any)).not.toThrow();
  });
});
