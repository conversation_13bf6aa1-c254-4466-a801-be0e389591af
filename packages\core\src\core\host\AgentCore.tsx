import React, { PropsWithChildren, useState } from "react";
import { useNavigate } from "react-router";

import { useCommandRunner, useSubscribeCommand } from "@/command";
import { MessageReceiver, MessageReceiverEvent } from "@/message";
import { BuildInCommand, HeaderMessagePackage, IMessagePackage, MessageStatus, Role } from "@/types";

import { createMessage } from "../common/message";
import { useAgentConfigs } from "../hooks/useAgent";
import { useHumanMessage, useIsLoadingMessage, useMessages } from "../state/store";
import { Transmitter } from "../transmitter/transmitter";
import { updateLoadingMessagePackage } from "./utils";

const AgentCore: React.FC<PropsWithChildren> = (props) => {
  const { children } = props;
  const [transmitter, setTransmitter] = useState<Transmitter | null>(null);
  const agentConfigs = useAgentConfigs();
  const { create: createHumanMessage } = useHumanMessage();
  const [, setMessages] = useMessages();
  const navigate = useNavigate();
  const [, setIsLoadingMessage] = useIsLoadingMessage();
  const runner = useCommandRunner();

  // 取消上一次的请求
  const cancelStaleChatRequest = ($transmitter?: Transmitter | null) => {
    if ($transmitter) {
      $transmitter.cancel();
    }
    setTransmitter(null);
    setIsLoadingMessage(false);
    setMessages((messages) => {
      const message = messages[messages.length - 1];
      if (!message) {
        return messages;
      }
      message.status = MessageStatus.Finished;
      return [...messages];
    });
  };

  useSubscribeCommand(BuildInCommand.SendMessage, (params) => {
    const { message, agentCode, conversationId, isNewConversation } = params;
    cancelStaleChatRequest();

    createHumanMessage(message);

    const config = agentConfigs.find((i) => i.code === agentCode);
    const url = config?.request.chat.url;
    const $transmitter = new Transmitter(url);

    // 发送消息
    $transmitter
      .send({ message, agentCode, conversationId })
      .then((response) => {
        const messageReceiver = new MessageReceiver(response.data);

        messageReceiver.receive();

        setIsLoadingMessage(true);

        // 监听接收到消息头事件
        messageReceiver.on(MessageReceiverEvent.HEADER_RECEIVED, (packageObj: IMessagePackage) => {
          let data;
          try {
            data = JSON.parse(packageObj.data) as HeaderMessagePackage;
            // 创建新消息，并设置 isFresh = true;
            const message = createMessage(data.message_id, Role.AI, [], MessageStatus.Loading, agentCode);
            message.isFresh = true;
            setMessages((messages) => {
              // 将历史消息 isFresh 设置为 false
              messages.forEach((i) => {
                i.isFresh = false;
              });
              return [...messages, message]
            });

            const conversationId = data.conversation_id;
            // TODO 切换会话逻辑可以优化
            // 通过当前路由判断是否跳转到对话详情页，chat/:id，跳转后自动切换当前会话
            if (isNewConversation) {
              runner(BuildInCommand.NewConversationCreated, { conversationId });
              navigate(`/chat/${conversationId}`);
            }
          } catch (error) {
            // TODO 异常处理
            console.error("Failed to parse structured data (in progress):", error);
          }
        });

        // 监听消息完成事件
        messageReceiver.on(MessageReceiverEvent.MESSAGE_FINISHED, () => {
          cancelStaleChatRequest($transmitter);
          // 将最后的消息状态设置为 finished
          setMessages((messages) => {
            const message = messages[messages.length - 1];
            if (!message) {
              return messages;
            }
            message.status = MessageStatus.Finished;
            return [...messages];
          });
        });

        // 监听正在接收消息包事件
        messageReceiver.on(MessageReceiverEvent.PACKAGE_RECEIVING, (packageObj: IMessagePackage) => {
          // 更新对应的消息
          setMessages((messages) => {
            return updateLoadingMessagePackage(messages, packageObj);
          });
        });

        // 监听消息包接收完成事件
        messageReceiver.on(MessageReceiverEvent.PACKAGE_FINISHED_RECEIVED, () => {
          // 如果没有 loading 状态的
        });

        // 监听消息包异常
        messageReceiver.on(MessageReceiverEvent.ERROR, () => {
          // 如果没有 loading 状态的
          cancelStaleChatRequest($transmitter);
          setMessages((messages) => {
            const message = messages[messages.length - 1];
            message.status = MessageStatus.Error;
            return [...messages];
          });
        });
      })
      .catch(() => {
        cancelStaleChatRequest($transmitter);
      });

    setTransmitter($transmitter);
  });

  useSubscribeCommand(BuildInCommand.CancelChatRequest, () => cancelStaleChatRequest(transmitter));

  return <>{children}</>;
};

export default AgentCore;
