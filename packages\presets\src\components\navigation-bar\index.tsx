import React, { useMemo } from "react";

import { useActiveAgentCode, useAgentConfigs } from "@cscs-agent/core";

const NavigationBar: React.FC = () => {
  const [currentAgentCode] = useActiveAgentCode();
  const agentConfigs = useAgentConfigs();

  const name = useMemo(() => {
    return agentConfigs.find((i) => i.code === currentAgentCode)?.name;
  }, [agentConfigs, currentAgentCode]);

  return <div className="presets:px-6 presets:py-3 presets:text-[rgba(37,45,62,0.85)]">{name}</div>;
};

export default NavigationBar;
