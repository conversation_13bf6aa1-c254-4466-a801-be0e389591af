import React from "react";

import { getAssetUrl, useActiveAgentConfig } from "@cscs-agent/core";

const Welcome: React.FC = () => {
  const agentConfig = useActiveAgentConfig();

  return (
    <div className="presets:p-4">
      <img width="64px" src={getAssetUrl(agentConfig?.logo)} className="presets:mx-auto presets:block presets:mb-3" />
      <div className="presets:text-2xl presets:font-bold presets:text-[rgba(37,45,62,0.85)]  presets:text-center">
        {agentConfig?.welcome}
      </div>
      {agentConfig?.description && (
        <div
          className="presets:w-[800px] presets:h-[70px] presets:text-sm presets:mt-8 presets:p-6 presets:text-[rgba(37,45,62,0.85)] presets:bg-white presets:rounded-lg"
          style={{
            boxShadow: "0px 2px 17px 2px rgba(185,210,246,0.15), 0px 8px 17px -3px rgba(185,210,246,0.1)",
          }}
        >
          {agentConfig?.description}
        </div>
      )}
    </div>
  );
};

export default Welcome;
