import { Button } from "antd";
import React, { useMemo } from "react";

import { useActiveAgentConfig } from "@/core";
import { CloseOutlined } from "@ant-design/icons";

interface HeaderPanelProps {
  open: boolean;
  onClose: () => void;
}

const HeaderPanel: React.FC<HeaderPanelProps> = (props) => {
  const { open, onClose } = props;
  const agentConfig = useActiveAgentConfig();

  const Components = useMemo(() => {
    const widgets = agentConfig?.sender?.slots?.headerPanel?.widgets ?? [];
    return widgets.map((i) => i.component);
  }, [agentConfig]);

  return (
    <div
      className={`ag:relative ${open ? "ag:block ag:transition-all ag:duration-300 ag:delay-100 ag:translate-y-0 ag:opacity-100" : "ag:height-0 ag:translate-y-5 ag:overflow-hidden ag:opacity-0"}`}
    >
      <div className="ag:w-full ag:absolute ag:z-50 ag:-translate-y-full ag:left-0 ag:right-0 ag:overflow-y-auto ag:bg-white ag:rounded-lg ag:shadow-box">
        <div className="ag:flex ag:w-full ag:items-center ag:justify-between ag:px-4 ag:py-3">
          <div className="ag:text-md ag:font-bold">模板</div>
          <Button
            type="text"
            size="small"
            icon={<CloseOutlined className="presets:text-gray-500" />}
            onClick={onClose}
          />
        </div>
        <div className="ag:flex ag:px-4 ag:pt-2 ag:pb-3">
          {Components.map((Component, index) => (
            <Component key={index} />
          ))}
        </div>
      </div>
    </div>
  );
};

export default HeaderPanel;
