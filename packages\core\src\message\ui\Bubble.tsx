import { Flex, Space } from "antd";
import React from "react";

import { useActiveAgentConfig } from "@/core/hooks/useAgent";
import { IMessage, MessageStatus, Role } from "@/types";

import { MessageRender } from "../render";
import MessageContext from "./Context";

interface BubbleProps {
  message: IMessage;
  messageRender?: (content: string) => React.ReactNode;
  avatar: {
    icon: React.ReactNode;
    style?: React.CSSProperties;
  };
  placement: "start" | "end";
  role: Role;
}

const Bubble: React.FC<BubbleProps> = (props) => {
  const { message, placement, role } = props;

  return (
    <MessageContext.Provider value={{ message }}>
      <Flex align="start" justify={placement === "end" ? "flex-end" : "flex-start"} className="ag:w-full">
        <div className="ag:max-w-[80%]">
          <div className={`ag:p-3 ag:rounded-lg ag:mr-2 ${placement === "end" ? "ag:bg-[rgba(37,45,62,0.06)]" : ""}`}>
            <Header />
            <MessageRender data={message} />
            {message.status === MessageStatus.Loading && <div className="ag:message-loader ag:mt-4"></div>}
          </div>
          <div className="ag:ml-3">
            <Footer role={role} />
          </div>
        </div>
      </Flex>
    </MessageContext.Provider>
  );
};

const Header: React.FC = () => {
  const agentConfig = useActiveAgentConfig();

  return (
    <Space>
      {agentConfig?.message?.slots?.header?.widgets?.map((Widget) => <Widget.component key={Widget.code} />)}
    </Space>
  );
};

const Footer: React.FC<{ role: Role }> = (props) => {
  const { role } = props;
  const agentConfig = useActiveAgentConfig();

  return (
    <Space className="ag:text-light">
      {agentConfig?.message?.slots?.footer?.widgets?.map((Widget) => {
        if (Widget.role === role) {
          return <Widget.component key={Widget.code} />;
        }
      })}
    </Space>
  );
};

export default Bubble;
