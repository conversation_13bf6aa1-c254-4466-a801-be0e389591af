import React, { forwardRef, useImperativeHandle } from "react";

import { EditorContent, useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";

import EditableTag from "./EditableTag";
import Select from "./Select";
import Tag from "./Tag";

interface EditorProps {
  onChange: (value: string) => void;
  onEnterPress?: (event: KeyboardEvent) => void;
  ref?: React.Ref<EditorRef>;
}

export interface EditorRef {
  focus: () => void;
  clear: () => void;
  insertText: (text: string) => void;
  getText: () => string;
}

const Editor = forwardRef<EditorRef, EditorProps>(function Editor(props, ref) {
  const { onChange, onEnterPress } = props;

  const editor = useEditor({
    extensions: [StarterKit, Tag, EditableTag, Select],
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML());
    },
    editorProps: {
      attributes: {
        style: "width: 100%; min-height: 106px; font-size: 14px;",
      },
      handleDOMEvents: {
        keydown: (_view, event) => {
          if (event.key === "Enter" && onEnterPress) {
            onEnterPress(event);
          }
          return false; // 让事件继续传播，保持默认行为
        },
      },
    },
  });

  useImperativeHandle(
    ref,
    () => ({
      focus: () => {
        editor?.commands.focus();
      },
      clear: () => {
        editor?.commands.clearContent();
      },
      insertText: (text: string) => {
        editor?.commands.insertContent(text);
      },
      getText: () => {
        // TODO 换行没有生效
        return (
          editor?.getText({
            blockSeparator: "\n\n",
          }) ?? ""
        );
      },
    }),
    [editor],
  );

  return <EditorContent editor={editor} />;
});

export default Editor;
