import { <PERSON><PERSON>, Divider, message } from "antd";
import React, { useMemo } from "react";

import { CloseOutlined, SaveOutlined } from "@ant-design/icons";
import { BuildInCommand, post, useCommandRunner } from "@cscs-agent/core";

interface PreviewProps {
  id: string;
  name: string;
  saveApiUrl: string | (() => string);
  previewUrl: string | ((id: string) => string);
}

interface StandardResponse {
  success: boolean;
  errorMessage?: string;
  data?: any;
}

const Preview: React.FC<PreviewProps> = (props) => {
  const { id, name, saveApiUrl, previewUrl } = props ?? {};
  const runner = useCommandRunner();

  const $previewUrl = useMemo(() => {
    if (typeof previewUrl === "function") {
      return previewUrl(id);
    }
    return previewUrl;
  }, [previewUrl, id]);

  const save = () => {
    const url = typeof saveApiUrl === "function" ? saveApiUrl() : saveApiUrl;

    post<StandardResponse>(url, { dynamicId: id })
      .then((res) => {
        if (!res.data.success) {
          message.error(res.data.errorMessage);
        } else {
          message.success("保存成功");
        }
      })
      .catch((error) => {
        message.error(error.message);
      });
  };

  const close = () => {
    runner(BuildInCommand.CloseSidePanel);
  };

  return (
    <div className="agents:h-full agents:w-full agents:flex agents:flex-col">
      <div className="agents:top-0 agents:bg-white agents:flex agents:items-center agents:justify-between agents:px-6 agents:py-3 agents:border-b agents:border-gray-100">
        <span>{name}</span>
        <div>
          <Button variant="text" onClick={save} color="primary" icon={<SaveOutlined />} size="small">
            保存
          </Button>
          <Divider type="vertical" />
          <Button
            variant="text"
            onClick={close}
            color="default"
            icon={<CloseOutlined style={{ color: "rgba(37,45,62,0.45)" }} />}
            size="small"
          />
        </div>
      </div>
      <div className="agents:flex-1">
        <iframe src={$previewUrl} width={500} className="agents:h-full agents:w-full" />
      </div>
    </div>
  );
};

export default Preview;
