import { ConfigProvider } from "antd";
import zhCN from "antd/locale/zh_CN";
import { useEffect } from "react";
import { Outlet, useMatch, useNavigate } from "react-router";

import { AgentChatHost } from "@cscs-agent/core";
import { DefaultBasicLayout } from "@cscs-agent/presets";

import { config } from "../../agent-config";

const Home = () => {
  const matched = useMatch("/");
  const navigate = useNavigate();

  useEffect(() => {
    // 设置默认智能体，跳转到 /agent/${code}
    if (matched) {
      navigate("/agent/dynamic-page-creator", { replace: true });
    }
  }, [matched]);

  return (
    <div className="app flex h-screen w-full">
      <ConfigProvider locale={zhCN}>
        <AgentChatHost config={config}>
          <DefaultBasicLayout main={<Outlet />} />
        </AgentChatHost>
      </ConfigProvider>
    </div>
  );
};

export default Home;
