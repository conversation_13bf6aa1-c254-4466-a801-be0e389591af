import { Tooltip } from "antd";
import React from "react";

import { Node, NodeViewProps, mergeAttributes } from "@tiptap/core";
import { NodeViewWrapper } from "@tiptap/react";
import { ReactNodeViewRenderer } from "@tiptap/react";

interface TagProps {
  text: string;
  rawValue: string;
  tooltips?: string;
}

/**
 * Tag 内元素设置 contentEditable，删除行内标签后面文本时自动 focus 到标签内。
 */
const TagComponent: React.FC<NodeViewProps> = (props) => {
  const { node } = props;

  const { text, tooltips } = node.attrs as TagProps;

  return (
    <NodeViewWrapper as="span">
      <Tooltip title={tooltips}>
        <span
          className="ag:px-2 ag:py-1 ag:rounded-md ag:bg-[rgba(0,0,0,0.05)] ag:text-[rgba(0,0,0,0.65)]"
          contentEditable={false}
        >
          {text}
        </span>
      </Tooltip>
    </NodeViewWrapper>
  );
};

const Tag = Node.create({
  name: "Tag",
  content: "inline*", // 使标签自动 focus
  group: "inline",
  inline: true,
  atom: true,

  addAttributes() {
    return {
      text: {
        default: "",
      },
      rawValue: {
        default: "",
      },
      tooltips: {
        default: "",
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: "embedded-tag",
      },
    ];
  },

  renderText(props) {
    return props.node.attrs.rawValue;
  },

  renderHTML({ HTMLAttributes }) {
    return ["embedded-tag", mergeAttributes(HTMLAttributes)];
  },

  addNodeView() {
    return ReactNodeViewRenderer(TagComponent as any, {});
  },
});

export default Tag;
