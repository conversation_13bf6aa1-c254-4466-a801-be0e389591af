import React, { useMemo } from "react";

import { AgentList, Conversation, useAgentConfigs } from "@cscs-agent/core";

import LogoHeader from "./LogoHeader";

interface DefaultLayoutProps {
  sidebar?: {
    agents: React.ReactNode;
    conversations: React.ReactNode;
  };
  main?: React.ReactNode;
}

const DefaultBasicLayout: React.FC<DefaultLayoutProps> = (props) => {
  const { sidebar, main } = props;
  const agentConfigs = useAgentConfigs();

  const height = useMemo(() => {
    if (agentConfigs.length > 9) {
      return `calc(100vh - 425px)`;
    } else {
      const height = 36 * agentConfigs.length;
      return `calc(100vh - 106px - ${height}px)`;
    }
  }, [agentConfigs.length]);

  const agents = useMemo(() => {
    return sidebar?.agents ?? <AgentList />;
  }, [sidebar?.agents]);

  const conversations = useMemo(() => {
    return sidebar?.conversations ?? <Conversation height={height} />;
  }, [sidebar?.conversations, height]);

  const mainContent = useMemo(() => {
    return main ?? <div>main</div>;
  }, [main]);

  return (
    <div className="presets:flex presets:h-full presets:w-full">
      {/* Left sidebar - fixed width 280px */}
      <div className="presets:w-[260px] presets:min-w-[260px] presets:h-full presets:border-r presets:border-gray-200 presets:overflow-hidden presets:flex presets:flex-col presets:bg-light-gray">
        <LogoHeader />
        <div className="presets:flex-1 presets:overflow-y-auto">
          <div className="presets:pr-2 presets:pl-4">{agents}</div>
        </div>
        <div className="presets:mx-2 presets:border-t presets:border-gray-200" />
        <div>{conversations}</div>
      </div>

      {/* Middle content - adaptive width */}
      <div className="presets:flex-1 presets:h-full">{mainContent}</div>
    </div>
  );
};

export default DefaultBasicLayout;
