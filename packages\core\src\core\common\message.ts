import { IMessage, IMessagePackage, MessageStatus, Role } from "@/types";

export class Message implements IMessage {
  id = "";
  content: IMessagePackage[] = [];
  role: Role = Role.AI;
  status: MessageStatus = MessageStatus.Loading;
  agentCode = "";
  isFresh = false;
  user_rating?: "like" | "dislike";

  constructor(
    id: string,
    role: Role,
    content: IMessagePackage[],
    status: MessageStatus,
    agentCode: string,
    user_rating?: "like" | "dislike",
  ) {
    this.id = id;
    this.role = role;
    this.content = content;
    this.status = status;
    this.agentCode = agentCode;
    this.user_rating = user_rating;
  }
}

/**
 * 创建 Message 实例的工厂函数
 * @param id 消息的唯一标识符
 * @param role 消息发送者的角色
 * @param content 组成消息的内容包数组
 * @param status 消息的状态
 * @param agentCode AgentCode
 * @param user_rating 用户评分（可选）
 * @returns Message 实例
 */
export function createMessage(
  id: string,
  role: Role,
  content: IMessagePackage[],
  status: MessageStatus,
  agentCode: string,
  user_rating?: "like" | "dislike",
): Message {
  const message = new Message(id, role, content, status, agentCode, user_rating);
  return message;
}
