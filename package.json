{"name": "cscs-agent", "version": "0.0.0", "private": true, "description": "", "main": "index.js", "scripts": {"build": "turbo run build", "dev": "turbo run dev", "dev:packages": "turbo run dev --filter=@cscs-agent/core --filter=@cscs-agent/presets --filter=@cscs-agent/agents", "lint": "turbo run lint", "lint:root": "eslint . --ext .js,.jsx,.ts,.tsx", "clean": "rimraf packages/*/dist"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {}, "devDependencies": {"@eslint/js": "^9.25.1", "@typescript-eslint/eslint-plugin": "^8.31.0", "@typescript-eslint/parser": "^8.31.0", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^15.15.0", "prettier": "^3.5.3", "rimraf": "^6.0.1", "turbo": "^2.5.2"}, "engines": {"node": ">=22.15.0", "pnpm": ">=10.9.0"}, "packageManager": "pnpm@10.9.0"}