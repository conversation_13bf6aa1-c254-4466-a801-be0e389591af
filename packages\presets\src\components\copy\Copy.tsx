import { Button } from "antd";
import React from "react";

import { CopyOutlined } from "@ant-design/icons";

const Copy: React.FC = () => {
  const handleCopy = () => {};

  return (
    <Button
      type="text"
      size="small"
      icon={
        <span className="presets:text-light">
          <CopyOutlined onClick={handleCopy} />
        </span>
      }
    ></Button>
  );
};

export default Copy;
