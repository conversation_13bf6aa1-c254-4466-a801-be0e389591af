import { useThrottleFn } from "ahooks";
import React, { useEffect, useRef } from "react";

import { BuildInCommand, useMessages, useSubscribeCommand } from "@cscs-agent/core";

interface DefaultChatLayoutProps {
  main: {
    navigationBar: React.ReactNode;
    message: React.ReactNode;
    sender: React.ReactNode;
  };
  sidePanel: React.ReactNode;
}

const DefaultChatLayout: React.FC<DefaultChatLayoutProps> = (props) => {
  const { main, sidePanel } = props;
  const messageContainerRef = useRef<HTMLDivElement>(null);
  const scrollWrapRef = useRef<HTMLDivElement>(null);
  const [sidePanelIsOpen, setSidePanelIsOpen] = React.useState(false);
  const [disableAutoScroll, setDisableAutoScroll] = React.useState(false);
  const [messages] = useMessages();

  // 自动滚动到底部
  const { run: debouncedScrollToBottom } = useThrottleFn(
    () => {
      if (disableAutoScroll) return;
      if (scrollWrapRef.current) {
        scrollWrapRef.current.scrollTo({
          top: scrollWrapRef.current.scrollHeight,
          behavior: "smooth",
        });
      }
    },
    { wait: 500 },
  );

  useEffect(() => {
    // 监听消息容器高度变化
    const messageContainer = messageContainerRef.current;
    if (!messageContainer) return;

    const resizeObserver = new ResizeObserver(() => {
      debouncedScrollToBottom();
    });
    resizeObserver.observe(messageContainer);

    // 用户手动滚动时，关闭自动滚动
    const scrollWrap = scrollWrapRef.current;
    if (scrollWrap) {
      const scrollListener = () => {
        setDisableAutoScroll(true);
      };

      scrollWrap.addEventListener("onwheel", scrollListener);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, [debouncedScrollToBottom]);

  useEffect(() => {
    // 每当消息变化时，开启自动滚动
    setDisableAutoScroll(false);
  }, [messages]);

  useSubscribeCommand(BuildInCommand.OpenSidePanel, () => {
    setSidePanelIsOpen(true);
  });

  useSubscribeCommand(BuildInCommand.CloseSidePanel, () => {
    setSidePanelIsOpen(false);
  });

  return (
    <div
      className={`presets:flex presets:h-full presets:w-full ${sidePanelIsOpen ? "presets:bg-[#f8f9fb]" : "presets:bg-white"}`}
    >
      {/* Middle content - adaptive width */}
      <div className={`presets:h-full presets:flex presets:flex-col ${sidePanelIsOpen ? "" : "presets:flex-1"}`}>
        <div>{main.navigationBar}</div>
        <div
          ref={scrollWrapRef}
          className="presets:flex-1 presets:w-full presets:mx-auto presets:p-4 presets:overflow-y-auto"
        >
          <div
            ref={messageContainerRef}
            className={`${sidePanelIsOpen ? "presets:w-[600px]" : "presets:w-[800px]"} presets:mx-auto`}
          >
            {main.message}
          </div>
        </div>
        <div
          className={`presets:pb-4 presets:mb-4  presets:mx-auto ${sidePanelIsOpen ? "presets:w-[600px]" : "presets:w-[800px]"}`}
        >
          {main.sender}
        </div>
      </div>

      {/* Right side panel - toggleable */}
      <div
        className={`presets:h-full presets:border-l presets:border-gray-200 presets:transition-all presets:duration-300 ${sidePanelIsOpen ? "presets:flex-1 presets:overflow-auto" : "presets:w-[0] presets:overflow-hidden"}`}
      >
        {sidePanel}
      </div>
    </div>
  );
};

export default DefaultChatLayout;
