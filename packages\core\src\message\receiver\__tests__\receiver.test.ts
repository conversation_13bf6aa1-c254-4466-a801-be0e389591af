import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";

import { MessagePackageStatus } from "@/types";

import { MessageReceiver, MessageReceiverEvent } from "../receiver";
import { createMessageChunk, createMockResponse, createSSEData } from "./mocks";

describe("MessageReceiver", () => {
  // 模拟控制台错误输出
  beforeEach(() => {
    vi.spyOn(console, "error").mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("应该正确初始化", () => {
    // 创建一个空的响应对象
    const mockResponse = createMockResponse([]);
    const receiver = new MessageReceiver(mockResponse);

    // 验证初始状态
    expect(receiver.packages).toEqual([]);
    expect(receiver.packageId).toBe("");
    expect(receiver.packageType).toBeNull();
    expect(receiver.packageBuffer).toEqual([]);
    expect(receiver.chunkId).toBe(-1);
    expect(receiver.reader).not.toBeNull();
    expect(receiver.decoder).not.toBeNull();
  });

  it("应该正确处理单个消息块", async () => {
    // 创建一个包含单个消息块的响应
    const chunk = createMessageChunk("pkg1", "text", 0, true, "Hello, World!");
    const mockResponse = createMockResponse([createSSEData([chunk])]);

    const receiver = new MessageReceiver(mockResponse);

    // 监听事件
    const onChunkReceived = vi.fn();
    const onPackageReceived = vi.fn();
    const onDone = vi.fn();

    receiver.on(MessageReceiverEvent.CHUNK_RECEIVED, onChunkReceived);
    receiver.on(MessageReceiverEvent.PACKAGE_FINISHED_RECEIVED, onPackageReceived);
    receiver.on(MessageReceiverEvent.DONE, onDone);

    // 开始接收
    receiver.receive();

    // 等待异步操作完成
    await new Promise((resolve) => setTimeout(resolve, 100));

    // 验证事件触发
    expect(onChunkReceived).toHaveBeenCalledWith(chunk);
    expect(onPackageReceived).toHaveBeenCalledWith({
      id: "pkg1",
      type: "text",
      status: MessagePackageStatus.Finished,
      data: "Hello, World!",
    });
    expect(onDone).toHaveBeenCalled();

    // 验证接收器状态
    expect(receiver.packages).toHaveLength(1);
    expect(receiver.packages[0]).toEqual({
      id: "pkg1",
      type: "text",
      status: MessagePackageStatus.Finished,
      data: "Hello, World!",
    });
  });

  it("应该正确处理多个有序的消息块", async () => {
    // 创建多个有序的消息块
    const chunks = [
      createMessageChunk("pkg1", "text", 0, false, "Hello, "),
      createMessageChunk("pkg1", "text", 1, false, "World"),
      createMessageChunk("pkg1", "text", 2, true, "!"),
    ];

    const mockResponse = createMockResponse([createSSEData(chunks)]);
    const receiver = new MessageReceiver(mockResponse);

    // 监听事件
    const onChunkReceived = vi.fn();
    const onPackageReceiving = vi.fn();
    const onPackageReceived = vi.fn();

    receiver.on(MessageReceiverEvent.CHUNK_RECEIVED, onChunkReceived);
    receiver.on(MessageReceiverEvent.PACKAGE_RECEIVING, onPackageReceiving);
    receiver.on(MessageReceiverEvent.PACKAGE_FINISHED_RECEIVED, onPackageReceived);

    // 开始接收
    receiver.receive();

    // 等待异步操作完成
    await new Promise((resolve) => setTimeout(resolve, 100));

    // 验证事件触发
    expect(onChunkReceived).toHaveBeenCalledTimes(3);
    expect(onPackageReceiving).toHaveBeenCalledTimes(2); // 只有非最后一个块会触发
    expect(onPackageReceived).toHaveBeenCalledWith({
      id: "pkg1",
      type: "text",
      status: MessagePackageStatus.Finished,
      data: "Hello, World!",
    });

    // 验证接收器状态
    expect(receiver.packages).toHaveLength(1);
    expect(receiver.packages[0].data).toBe("Hello, World!");
  });

  it("应该正确处理多个无序的消息块", async () => {
    // 创建多个无序的消息块
    const chunks = [
      createMessageChunk("pkg1", "text", 1, false, "World"),
      createMessageChunk("pkg1", "text", 0, false, "Hello, "),
      createMessageChunk("pkg1", "text", 2, true, "!"),
    ];

    const mockResponse = createMockResponse([createSSEData(chunks)]);
    const receiver = new MessageReceiver(mockResponse);

    // 监听事件
    const onPackageReceived = vi.fn();
    receiver.on(MessageReceiverEvent.PACKAGE_FINISHED_RECEIVED, onPackageReceived);

    // 开始接收
    receiver.receive();

    // 等待异步操作完成
    await new Promise((resolve) => setTimeout(resolve, 100));

    // 验证事件触发
    expect(onPackageReceived).toHaveBeenCalledWith({
      id: "pkg1",
      type: "text",
      status: MessagePackageStatus.Finished,
      data: "Hello, World!",
    });

    // 验证接收器状态
    expect(receiver.packages).toHaveLength(1);
    expect(receiver.packages[0].data).toBe("Hello, World!");
  });

  it("应该正确处理多个消息包", async () => {
    // 创建多个消息包的块
    const chunks = [
      createMessageChunk("pkg1", "text", 0, true, "First package"),
      createMessageChunk("pkg2", "structured", 0, false, '{"key":'),
      createMessageChunk("pkg2", "structured", 1, true, '"value"}'),
    ];

    const mockResponse = createMockResponse([createSSEData(chunks)]);
    const receiver = new MessageReceiver(mockResponse);

    // 监听事件
    const onPackageReceived = vi.fn();
    receiver.on(MessageReceiverEvent.PACKAGE_FINISHED_RECEIVED, onPackageReceived);

    // 开始接收
    receiver.receive();

    // 等待异步操作完成
    await new Promise((resolve) => setTimeout(resolve, 100));

    // 验证事件触发次数
    expect(onPackageReceived).toHaveBeenCalledTimes(2);

    // 验证接收器状态
    expect(receiver.packages).toHaveLength(2);
    expect(receiver.packages[0].package_id).toBe("pkg1");
    expect(receiver.packages[0].package_type).toBe("text");
    expect(receiver.packages[0].data).toBe("First package");

    expect(receiver.packages[1].package_id).toBe("pkg2");
    expect(receiver.packages[1].package_type).toBe("structured");
    expect(receiver.packages[1].data).toBe('{"key":"value"}');
  });

  it("应该处理不完整的消息包", async () => {
    // 创建不完整的消息包（缺少最后一个块）
    const chunks = [
      createMessageChunk("pkg1", "text", 0, false, "Hello, "),
      createMessageChunk("pkg1", "text", 1, false, "World"),
      // 缺少 is_last=true 的块
      createMessageChunk("pkg2", "text", 0, true, "New package"),
    ];

    const mockResponse = createMockResponse([createSSEData(chunks)]);
    const receiver = new MessageReceiver(mockResponse);

    // 监听事件
    const onError = vi.fn();
    const onPackageReceived = vi.fn();

    receiver.on(MessageReceiverEvent.ERROR, onError);
    receiver.on(MessageReceiverEvent.PACKAGE_FINISHED_RECEIVED, onPackageReceived);

    // 开始接收
    receiver.receive();

    // 等待异步操作完成
    await new Promise((resolve) => setTimeout(resolve, 100));

    // 验证错误事件触发
    expect(onError).toHaveBeenCalled();

    // 验证第二个包被正确处理
    expect(onPackageReceived).toHaveBeenCalledWith({
      id: "pkg2",
      type: "text",
      status: MessagePackageStatus.Finished,
      data: "New package",
    });

    // 验证接收器状态
    expect(receiver.packages).toHaveLength(1);
    expect(receiver.packages[0].package_id).toBe("pkg2");
  });

  it("应该处理不连续的消息块", async () => {
    // 创建不连续的消息块（缺少中间的块）
    const chunks = [
      createMessageChunk("pkg1", "text", 0, false, "Hello, "),
      // 缺少 chunk_id=1 的块
      createMessageChunk("pkg1", "text", 2, true, "!"),
    ];

    const mockResponse = createMockResponse([createSSEData(chunks)]);
    const receiver = new MessageReceiver(mockResponse);

    // 监听事件
    const onError = vi.fn();

    receiver.on(MessageReceiverEvent.ERROR, onError);

    // 开始接收
    receiver.receive();

    // 等待异步操作完成
    await new Promise((resolve) => setTimeout(resolve, 100));

    // 验证错误事件触发
    expect(onError).toHaveBeenCalled();

    // 验证接收器状态
    expect(receiver.packages).toHaveLength(0);
  });

  it("应该正确处理停止接收", async () => {
    // 创建一个长消息
    const chunks = [
      createMessageChunk("pkg1", "text", 0, false, "This is "),
      createMessageChunk("pkg1", "text", 1, false, "a long "),
      createMessageChunk("pkg1", "text", 2, true, "message."),
    ];

    // 模拟一个延迟返回的响应
    const mockReader = {
      read: vi.fn().mockImplementation(() => {
        return new Promise((resolve) => {
          setTimeout(() => {
            resolve({ value: createSSEData([chunks[0]]), done: false });
          }, 50);
        });
      }),
      cancel: vi.fn().mockResolvedValue(undefined),
    };

    const mockResponse = {
      data: {
        body: {
          getReader: () => mockReader,
        },
      },
      status: 200,
      statusText: "OK",
      headers: {},
      config: {} as any,
    };

    const receiver = new MessageReceiver(mockResponse as any);

    // 监听事件
    const onChunkReceived = vi.fn();
    receiver.on(MessageReceiverEvent.CHUNK_RECEIVED, onChunkReceived);

    // 开始接收
    receiver.receive();

    // 立即停止接收
    receiver.stop();

    // 等待一段时间
    await new Promise((resolve) => setTimeout(resolve, 100));

    // 验证 cancel 被调用
    expect(mockReader.cancel).toHaveBeenCalled();
  });
});
