import { resolve } from "path";

import { defineConfig } from "vite";
import svgr from "vite-plugin-svgr";

import typescript from "@rollup/plugin-typescript";
import tailwindcss from "@tailwindcss/vite";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  plugins: [
    typescript({
      tsconfig: resolve(__dirname, mode === "development" ? "tsconfig.json" : "tsconfig.build.json"),
    }),
    tailwindcss(),
    svgr({
      include: "**/*.svg?react",
    }),
  ],
  build: {
    lib: {
      entry: resolve(__dirname, "src/index.ts"),
      name: "@cscs-agent/presets",
      fileName: (format) => `index.${format}.js`,
      formats: ["es", "cjs"],
    },
    sourcemap: mode === "development",
    outDir: "dist",
    rollupOptions: {
      // Make sure to externalize deps that shouldn't be bundled
      external: [
        "ahooks",
        "nanoid",
        // Peer dependencies
        "@cscs-agent/core",
        "@ant-design/icons",
        "antd",
        "antd-style",
        "react",
        "react-dom",
        "react-router",
        "react/jsx-runtime",
      ],
      output: {
        // Provide global variables to use in the UMD build
        globals: {
          ahooks: "ahooks",
          nanoid: "nanoid",
          // Peer dependencies
          "@cscs-agent/core": "CscsAgentCore",
          "@ant-design/icons": "AntDesignIcons",
          antd: "antd",
          "antd-style": "antdStyle",
          react: "React",
          "react-dom": "ReactDOM",
          "react/jsx-runtime": "ReactJSXRuntime",
        },
        assetFileNames: "presets-tailwind.css",
      },
    },
  },
  resolve: {
    alias: {
      "@": resolve(__dirname, "src"),
    },
  },
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
      },
    },
  },
}));
