import React from "react";
import Markdown from "react-markdown";
import rehypeHighlight from "rehype-highlight";
import rehypeRaw from "rehype-raw";
import remarkGfm from "remark-gfm";

import { BlockStatus } from "@/types";

interface ThinkingBlockProps {
  content: string;
  status: BlockStatus;
}

const ThinkingBlock: React.FC<ThinkingBlockProps> = (props) => {
  const { content, status } = props;

  return (
    <div className="ag:mb-2">
      <div className="">{status === BlockStatus.Loading ? "思考中..." : "思考完成"}</div>
      <div className="ag:break-all ag:leading-[1.6em] ag:text-gray-400">
        <Markdown rehypePlugins={[rehypeHighlight, rehypeRaw]} remarkPlugins={[remarkGfm]}>
          {content}
        </Markdown>
      </div>
    </div>
  );
};

export default ThinkingBlock;
