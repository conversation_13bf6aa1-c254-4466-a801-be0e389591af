import { IMessage, IMessagePackage, MessageStatus } from "@/types";

export function updateLoadingMessagePackage(messages: IMessage[], packageObj: IMessagePackage) {
  const message = messages[messages.length - 1];
  if (message && message.status === MessageStatus.Loading) {
    // 如果最后一条package的id等于当前消息的id且状态为loading，则更新package
    if (message.content.length > 0) {
      const lastPackage = message.content[message.content.length - 1];
      if (lastPackage.package_id === packageObj.package_id) {
        message.content.pop();
      }
    }
    // 插入最新的package
    message.content.push(packageObj);
  }
  return [...messages];
}
