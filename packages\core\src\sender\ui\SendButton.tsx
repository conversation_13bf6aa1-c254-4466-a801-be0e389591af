import { Button } from "antd";
import React from "react";

import { Icon } from "@cscs-agent/icons";

interface SendButtonProps {
  onClick: () => Promise<unknown> | void;
  onCancel: () => Promise<unknown> | void;
  disabled?: boolean;
  isLoading?: boolean;
}

interface LoadingButtonProps {
  onCancel: () => Promise<unknown> | void;
}

// enum SendButtonStatus {
//   Idle = "idle",
//   Loading = "loading",
//   Success = "success",
//   Error = "error",
// }

const SendButton: React.FC<SendButtonProps> = (props) => {
  const { onClick, onCancel, disabled, isLoading } = props;

  const handleClick = () => {
    if (disabled) return;
    onClick();
  };

  const handleCancel = () => {
    onCancel();
  };

  if (isLoading) {
    return <LoadingButton onCancel={handleCancel} />;
  }

  return (
    <Button
      shape="circle"
      type={"primary"}
      icon={<Icon icon="ArrowUp" style={{ color: "#ffffff" }} />}
      className="ag:ml-2"
      style={
        disabled
          ? {
              background: "rgba(37, 45, 62, 0.25)",
              cursor: "not-allowed",
            }
          : {}
      }
      onClick={handleClick}
    />
  );
};

const LoadingButton: React.FC<LoadingButtonProps> = (props) => {
  const { onCancel } = props;

  return (
    <Button
      shape="circle"
      type="text"
      icon={<Icon icon="Stop" style={{ color: "rgb(108, 144, 242)", fontSize: "32px" }} />}
      className="ag:ml-2"
      onClick={onCancel}
    />
  );
};

export default SendButton;
