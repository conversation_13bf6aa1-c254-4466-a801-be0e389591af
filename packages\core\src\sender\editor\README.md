# Editor Component - 回车事件监听功能

## 概述

Editor组件现在支持监听回车键事件，允许开发者在用户按下回车键时执行自定义逻辑。

## 功能特性

- ✅ 监听回车键按下事件
- ✅ 支持区分普通回车和Shift+回车
- ✅ 可选的回调函数，不影响现有功能
- ✅ 保持TipTap编辑器的默认行为
- ✅ 完整的TypeScript类型支持

## API

### EditorProps

```typescript
interface EditorProps {
  onChange: (value: string) => void;
  onEnterPress?: (event: KeyboardEvent) => void; // 新增的回车事件回调
  ref?: React.Ref<EditorRef>;
}
```

### 参数说明

- `onEnterPress` (可选): 当用户按下回车键时触发的回调函数
  - 参数: `event: KeyboardEvent` - 原生键盘事件对象
  - 可以通过 `event.shiftKey` 检查是否同时按下了Shift键
  - 可以通过 `event.preventDefault()` 阻止默认行为

## 使用示例

### 基础用法

```typescript
import React from 'react';
import Editor from './Editor';

const MyComponent = () => {
  const handleChange = (value: string) => {
    console.log('内容变化:', value);
  };

  const handleEnterPress = (event: KeyboardEvent) => {
    console.log('用户按下了回车键');
    
    // 检查是否是Shift+Enter（通常用于换行）
    if (event.shiftKey) {
      console.log('Shift+Enter: 允许换行');
      return;
    }
    
    // 普通回车键：执行提交逻辑
    console.log('Enter: 执行提交');
  };

  return (
    <Editor 
      onChange={handleChange}
      onEnterPress={handleEnterPress}
    />
  );
};
```

### 高级用法 - 自动提交消息

```typescript
import React, { useRef } from 'react';
import Editor, { EditorRef } from './Editor';

const ChatInput = () => {
  const editorRef = useRef<EditorRef>(null);

  const handleEnterPress = (event: KeyboardEvent) => {
    // Shift+Enter: 允许换行，不提交
    if (event.shiftKey) {
      return;
    }
    
    // 普通Enter: 阻止默认行为并提交消息
    event.preventDefault();
    
    // 获取编辑器内容
    const message = editorRef.current?.getText() ?? '';
    
    // 检查是否有内容可以提交
    if (message.trim() !== '') {
      // 提交消息
      submitMessage(message);
      
      // 清空编辑器
      editorRef.current?.clear();
    }
  };

  const submitMessage = (message: string) => {
    console.log('提交消息:', message);
    // 这里实现你的消息提交逻辑
  };

  return (
    <Editor 
      ref={editorRef}
      onChange={() => {}}
      onEnterPress={handleEnterPress}
    />
  );
};
```

### 在Sender组件中的实际应用

```typescript
// 在 Sender.tsx 中的实现示例
const handleEnterPress = (event: KeyboardEvent) => {
  // 如果是Shift+Enter，允许换行，不提交
  if (event.shiftKey) {
    return;
  }
  
  // 如果是普通Enter键，阻止默认行为并提交消息
  event.preventDefault();
  
  // 检查是否有内容可以提交且不在加载状态
  const message = editorRef.current?.getText() ?? "";
  if (message.trim() !== "" && !isLoadingMessage) {
    onSubmit();
  }
};

// 在JSX中使用
<Editor 
  onChange={setInputValue} 
  onEnterPress={handleEnterPress} 
  ref={editorRef} 
/>
```

## 注意事项

1. **默认行为**: 如果不提供 `onEnterPress` 回调，编辑器将保持原有的默认行为
2. **事件传播**: 回调函数返回 `false` 以确保事件继续传播，保持TipTap的默认功能
3. **Shift+Enter**: 通常用于在聊天应用中插入换行符而不提交消息
4. **类型安全**: 所有的事件处理都有完整的TypeScript类型支持

## 测试

组件包含完整的单元测试，覆盖以下场景：

- ✅ 基本渲染功能
- ✅ 回车键事件触发
- ✅ 非回车键不触发回调
- ✅ 未提供回调函数时的正常工作
- ✅ useEditor配置正确性

运行测试：

```bash
npm test -- Editor.test.tsx
```
