import React, { useEffect } from "react";

import { Node, NodeViewProps, mergeAttributes } from "@tiptap/core";
import { NodeViewContent, NodeViewWrapper } from "@tiptap/react";
import { ReactNodeViewRenderer } from "@tiptap/react";

interface EditableTagProps {
  placeholder?: string;
}

/**
 * Tag 内元素设置 contentEditable，删除行内标签后面文本时自动 focus 到标签内。
 */
const EditableTagComponent: React.FC<NodeViewProps> = (props) => {
  const { node } = props;
  const { placeholder } = node.attrs as EditableTagProps;
  const [hidePlaceholder, setHidePlaceholder] = React.useState(false);

  useEffect(() => {
    setHidePlaceholder(node.content.size > 1);
  }, [node.content]);

  return (
    <NodeViewWrapper
      as="span"
      className="ag:after:content-['\200B'] ag:px-2 ag:py-1 ag:rounded-md ag:bg-[rgba(0,0,0,0.05)] ag:text-[rgba(0,0,0,0.65)]"
    >
      {/* 添加一个不可编辑的空元素，避免删除最后字符跳出 node 造成异常 */}
      <span contentEditable={false}>&#xFEFF;</span>

      <NodeViewContent as="span" />

      <span className="ag:text-[rgba(0,0,0,0.35)]" contentEditable={false} hidden={hidePlaceholder}>
        {placeholder}
      </span>
    </NodeViewWrapper>
  );
};

const EditableTag = Node.create({
  name: "EditableTag",
  content: "text*",
  group: "inline",
  inline: true,

  addAttributes() {
    return {
      placeholder: {
        default: "",
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: "embedded-editable-tag",
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return ["embedded-editable-tag", mergeAttributes(HTMLAttributes), 0];
  },

  addNodeView() {
    return ReactNodeViewRenderer(EditableTagComponent as any);
  },
});

export default EditableTag;
