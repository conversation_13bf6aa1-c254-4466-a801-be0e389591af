import React, { useContext } from "react";
import { useNavigate } from "react-router";

import { AgentChatContext } from "@/core/state/context";
import { useActiveAgentCode, useActiveAgentMenuCode, useMessages } from "@/core/state/store";
import { getAssetUrl } from "@/utils/asset";

// Agent module exports
const AgentList: React.FC = () => {
  const { config } = useContext(AgentChatContext);
  const agents = config.agents ?? [];
  const [, setMessages] = useMessages();
  const navigate = useNavigate();
  const defaultLogoUrl = getAssetUrl("/assets/common-agent-logo.png");

  const [activeAgentMenuCode, setActiveAgentMenuCode] = useActiveAgentMenuCode();
  const [, setActiveAgentCode] = useActiveAgentCode();

  const handleAgentClick = (code: string) => {
    if (activeAgentMenuCode === code) return;
    navigate(`/agent/${code}`);
    setActiveAgentMenuCode(code);
    setActiveAgentCode(code);
    setMessages([]);
  };

  return (
    <ul className="ag:overflow-y-auto ag:overflow-x-hidden ag:h-full ag:pt-2">
      {agents.map((i) => {
        return (
          <li
            key={i.code}
            className={`ag:flex ag:items-center ag:cursor-pointer ag:p-2 ${
              activeAgentMenuCode === i.code ? "ag:bg-[rgba(237,238,239,1)]" : ""
            }`}
            onClick={() => handleAgentClick(i.code)}
          >
            <i className="ag:mr-2">
              <img src={i.logo ?? defaultLogoUrl} width="16" />
            </i>
            <span className="ag:text-sm ag:text-dark">{i.name}</span>
          </li>
        );
      })}
    </ul>
  );
};

export default AgentList;
