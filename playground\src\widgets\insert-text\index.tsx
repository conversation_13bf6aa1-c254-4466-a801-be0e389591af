import { Button } from "antd";

import { EditOutlined } from "@ant-design/icons";
import { BuildInCommand, useCommandRunner } from "@cscs-agent/core";

const template = `生成一个页面，查询数据库中的<embedded-editable-tag>企业信息表</embedded-editable-tag>，
在页面列表中展示 <embedded-editable-tag placeholder="请输入字段名称">&#xFEFF;</embedded-editable-tag>、<embedded-editable-tag>企业性质</embedded-editable-tag>、<embedded-editable-tag><span>统一社会信用代码</span></embedded-editable-tag>。
<embedded-select
  placeholder="请选择查询模式"
  options='${JSON.stringify([
    { label: "精确查询", value: "精确查询" },
    { label: "模糊查询", value: "模糊查询" },
  ])}'
  defaultValue="精确查询"
  tooltips="请选择页面类型"
></embedded-select>
`;

const InsertText = () => {
  const runner = useCommandRunner();

  return (
    <Button
      onClick={() =>
        runner(BuildInCommand.InsertTextIntoSender, {
          text: template,
        })
      }
      size="small"
      icon={<EditOutlined />}
    >
      插入模板
    </Button>
  );
};

export default InsertText;
